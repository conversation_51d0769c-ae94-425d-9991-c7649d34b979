import {
  type StreamingService,
  type StreamingChunk,
  type SendMessagePayload,
  type StreamingConfig,
  DEFAULT_API_CONFIG,
} from '../types/api';
import { mockStreamingService } from './mockStreamingService';

// Default streaming configuration
const DEFAULT_STREAMING_CONFIG: StreamingConfig = {
  enabled: true,
  chunkSize: 1024,
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
};

// Streaming throttling configuration
interface StreamingThrottleConfig {
  chunkDelay: number; // Delay between processing chunks (ms)
  characterDelay: number; // Delay between characters for typing effect (ms)
  toolCallDisplayTime: number; // How long to show tool call info (ms)
  agentDisplayTime: number; // How long to show agent info (ms)
  enabled: boolean; // Whether throttling is enabled
}

const DEFAULT_THROTTLE_CONFIG: StreamingThrottleConfig = {
  chunkDelay: parseInt(import.meta.env.VITE_STREAMING_CHUNK_DELAY || '100'),
  characterDelay: parseInt(import.meta.env.VITE_STREAMING_CHARACTER_DELAY || '25'),
  toolCallDisplayTime: parseInt(import.meta.env.VITE_STREAMING_TOOL_DISPLAY_TIME || '2000'),
  agentDisplayTime: parseInt(import.meta.env.VITE_STREAMING_AGENT_DISPLAY_TIME || '1500'),
  enabled: import.meta.env.VITE_STREAMING_THROTTLE_ENABLED !== 'false', // Default to enabled
};

// Implementation of StreamingService
class StreamingServiceImpl implements StreamingService {
  private config: StreamingConfig;
  private throttleConfig: StreamingThrottleConfig;

  constructor(config: Partial<StreamingConfig> = {}) {
    this.config = { ...DEFAULT_STREAMING_CONFIG, ...config };
    this.throttleConfig = { ...DEFAULT_THROTTLE_CONFIG };
  }

  async handleStreamingMessage(payload: SendMessagePayload): Promise<Response> {
    // Use mock service only when VITE_USE_MOCK_STREAMING is explicitly set to true
    if (import.meta.env.VITE_USE_MOCK_STREAMING === 'true') {
      console.log('🎭 Using mock streaming service');
      return mockStreamingService.createMockStreamingResponse(payload);
    }

    console.log('🚀 Using real streaming API:', DEFAULT_API_CONFIG.baseURL);
    const endpoint = `${DEFAULT_API_CONFIG.baseURL}/chat`;

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/plain',
          // Add authentication headers if available
          ...(import.meta.env.VITE_API_KEY && { 'X-API-Key': import.meta.env.VITE_API_KEY }),
          ...(import.meta.env.VITE_AUTH_TOKEN && { 'Authorization': `Bearer ${import.meta.env.VITE_AUTH_TOKEN}` }),
        },
        body: JSON.stringify({
          ...payload,
          stream: true, // Enable streaming
        }),
        signal: AbortSignal.timeout(this.config.timeout || 30000),
      });

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      if (!response.body) {
        throw new Error('No response body for streaming');
      }

      return response;
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Streaming request timed out');
        }
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          throw new Error('Network error: Unable to connect to the server');
        }
      }
      throw error;
    }
  }

  async* processStreamingResponse(response: Response): AsyncGenerator<StreamingChunk, void, unknown> {
    if (!response.body) {
      throw new Error('No response body for streaming');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let retryCount = 0;
    let isReceivingImage = false;
    let imageBuffer = '';

    try {
      while (true) {
        try {
          const { value, done } = await reader.read();
          if (done) {
            // If we were receiving an image when the stream ended, finalize it
            if (isReceivingImage && imageBuffer.trim()) {
              yield {
                type: 'image',
                content: imageBuffer.trim(),
                metadata: { imageEnd: true, streamEnd: true },
              };
            }
            break;
          }

          const chunk = decoder.decode(value, { stream: true });

          // Reset retry count on successful read
          retryCount = 0;

          // Track image state to handle stream end properly
          if (chunk.includes('[IMAGE]:')) {
            isReceivingImage = true;
            imageBuffer = '';
          }
          if (chunk.includes('[IMAGE_END]')) {
            isReceivingImage = false;
            imageBuffer = '';
          }

          // If we're currently receiving an image and this chunk doesn't contain any special markers,
          // treat it as image data
          if (isReceivingImage && !this.containsSpecialMarkers(chunk)) {
            imageBuffer += chunk;
            yield {
              type: 'image',
              content: chunk,
              metadata: { imageContinuation: true },
            };
            continue;
          }

          // Process chunk with throttling if enabled
          if (this.throttleConfig.enabled) {
            yield* this.parseChunkWithThrottling(chunk);
          } else {
            yield* this.parseChunk(chunk);
          }
        } catch (error) {
          retryCount++;

          if (retryCount >= (this.config.retryAttempts || 3)) {
            throw new Error(`Streaming failed after ${retryCount} attempts: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay || 1000));

          // Continue to next iteration to retry
          continue;
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Streaming was interrupted');
      }
      throw error;
    } finally {
      try {
        reader.releaseLock();
      } catch {
        // Ignore errors when releasing lock
      }
    }
  }

  // Helper method to check if chunk contains special markers
  private containsSpecialMarkers(chunk: string): boolean {
    const markers = ['[STARTED THREAD]:', '[TOOLCALL]', '[ENDTOOLCALL]', '[STARTED RESPONSE]:', '[IMAGE]:', '[IMAGE_END]'];
    return markers.some(marker => chunk.includes(marker));
  }

  private* parseChunk(chunk: string): Generator<StreamingChunk, void, unknown> {
    // Handle thread ID
    if (chunk.includes('[STARTED THREAD]:')) {
      const threadIdRegex = /\[STARTED THREAD\]:\s*([^[]+?)(?=\[|$)/;
      const match = chunk.match(threadIdRegex);
      if (match && match[1]) {
        const threadId = match[1].trim();
        yield {
          type: 'thread_id',
          content: threadId,
        };
      }

      // Continue processing the rest of the chunk
      const remainingChunk = chunk.replace(threadIdRegex, '');
      if (remainingChunk.trim()) {
        yield* this.parseChunk(remainingChunk);
      }
      return;
    }

    // Handle tool calls
    if (chunk.includes('[TOOLCALL]')) {
      const toolCallRegex = /\[TOOLCALL\]\s*([^[]+?)\s*\[ENDTOOLCALL\]/g;
      let match;
      let lastIndex = 0;

      while ((match = toolCallRegex.exec(chunk)) !== null) {
        const toolName = match[1].trim();
        if (toolName) {
          yield {
            type: 'tool_call',
            content: toolName,
          };
        }
        lastIndex = match.index + match[0].length;
      }

      // Continue processing the rest of the chunk after tool calls
      const remainingChunk = chunk.substring(lastIndex);
      if (remainingChunk.trim()) {
        yield* this.parseChunk(remainingChunk);
      }
      return;
    }

    // Handle response start
    if (chunk.includes('[STARTED RESPONSE]:')) {
      const responseRegex = /\[STARTED RESPONSE\]:\s*(.*)/;
      const responseMatch = chunk.match(responseRegex);

      yield {
        type: 'response_start',
        content: '',
      };

      if (responseMatch && responseMatch[1]) {
        const firstContentChunk = responseMatch[1];
        if (firstContentChunk.trim()) {
          // Check if the first content chunk contains an image
          if (firstContentChunk.includes('[IMAGE]:')) {
            yield* this.parseChunk(firstContentChunk);
          } else {
            yield {
              type: 'text',
              content: firstContentChunk,
            };
          }
        }
      }
      return;
    }

    // Handle images - improved logic to handle images without [IMAGE_END] markers
    if (chunk.includes('[IMAGE]:')) {
      const imageStartIndex = chunk.indexOf('[IMAGE]:');
      const textBeforeImage = chunk.substring(0, imageStartIndex);
      const imageDataStart = chunk.substring(imageStartIndex + '[IMAGE]:'.length);

      // Yield text before image if any
      if (textBeforeImage.trim()) {
        yield {
          type: 'text',
          content: textBeforeImage,
        };
      }

      // Check if there's an [IMAGE_END] marker in this chunk
      if (imageDataStart.includes('[IMAGE_END]')) {
        const endIndex = imageDataStart.indexOf('[IMAGE_END]');
        const imageData = imageDataStart.substring(0, endIndex);
        const remainingText = imageDataStart.substring(endIndex + '[IMAGE_END]'.length);

        // Complete image with end marker
        if (imageData.trim()) {
          yield {
            type: 'image',
            content: imageData.trim(),
            metadata: { imageStart: true, imageEnd: true },
          };
        }

        // Process remaining text if any
        if (remainingText.trim()) {
          yield {
            type: 'text',
            content: remainingText,
          };
        }
      } else {
        // Start image processing without end marker - the image will continue until stream ends or another tag appears
        yield {
          type: 'image',
          content: imageDataStart,
          metadata: { imageStart: true },
        };
      }
      return;
    }

    // Handle explicit image end (for backward compatibility)
    if (chunk.includes('[IMAGE_END]')) {
      const endIndex = chunk.indexOf('[IMAGE_END]');
      const imageData = chunk.substring(0, endIndex);
      const remainingText = chunk.substring(endIndex + '[IMAGE_END]'.length);

      // Complete image
      if (imageData.trim()) {
        yield {
          type: 'image',
          content: imageData.trim(),
          metadata: { imageEnd: true },
        };
      }

      // Yield remaining text if any
      if (remainingText.trim()) {
        yield {
          type: 'text',
          content: remainingText,
        };
      }
      return;
    }

    // Handle regular text chunks
    if (chunk.trim()) {
      yield {
        type: 'text',
        content: chunk,
      };
    }
  }

  // Throttled chunk parsing for natural typing effect
  private async* parseChunkWithThrottling(chunk: string): AsyncGenerator<StreamingChunk, void, unknown> {
    // Add delay between chunks for natural pacing
    await this.delay(this.throttleConfig.chunkDelay);

    // Handle thread ID (no throttling needed)
    if (chunk.includes('[STARTED THREAD]:')) {
      const threadIdRegex = /\[STARTED THREAD\]:\s*([^[]+?)(?=\[|$)/;
      const match = chunk.match(threadIdRegex);
      if (match && match[1]) {
        const threadId = match[1].trim();
        yield {
          type: 'thread_id',
          content: threadId,
        };
      }

      // Continue processing the rest of the chunk
      const remainingChunk = chunk.replace(threadIdRegex, '');
      if (remainingChunk.trim()) {
        yield* this.parseChunkWithThrottling(remainingChunk);
      }
      return;
    }

    // Handle tool calls with extended display time
    if (chunk.includes('[TOOLCALL]')) {
      const toolCallRegex = /\[TOOLCALL\]\s*([^[]+?)\s*\[ENDTOOLCALL\]/g;
      let match;
      let lastIndex = 0;

      while ((match = toolCallRegex.exec(chunk)) !== null) {
        const toolName = match[1].trim();
        if (toolName) {
          yield {
            type: 'tool_call',
            content: toolName,
          };
          // Give users time to read the tool call information
          await this.delay(this.throttleConfig.toolCallDisplayTime);
        }
        lastIndex = match.index + match[0].length;
      }

      // Continue processing the rest of the chunk after tool calls
      const remainingChunk = chunk.substring(lastIndex);
      if (remainingChunk.trim()) {
        yield* this.parseChunkWithThrottling(remainingChunk);
      }
      return;
    }

    // Handle response start
    if (chunk.includes('[STARTED RESPONSE]:')) {
      const responseRegex = /\[STARTED RESPONSE\]:\s*(.*)/;
      const responseMatch = chunk.match(responseRegex);

      yield {
        type: 'response_start',
        content: '',
      };

      // Give users time to see the response start
      await this.delay(this.throttleConfig.agentDisplayTime);

      if (responseMatch && responseMatch[1]) {
        const firstContentChunk = responseMatch[1];
        if (firstContentChunk.trim()) {
          // Check if the first content chunk contains an image
          if (firstContentChunk.includes('[IMAGE]:')) {
            yield* this.parseChunkWithThrottling(firstContentChunk);
          } else {
            // Stream the first content with character-by-character effect
            yield* this.streamTextWithTypingEffect(firstContentChunk);
          }
        }
      }
      return;
    }

    // Handle images - improved logic to handle images without [IMAGE_END] markers (no throttling for image data)
    if (chunk.includes('[IMAGE]:')) {
      const imageStartIndex = chunk.indexOf('[IMAGE]:');
      const textBeforeImage = chunk.substring(0, imageStartIndex);
      const imageDataStart = chunk.substring(imageStartIndex + '[IMAGE]:'.length);

      // Stream text before image with typing effect
      if (textBeforeImage.trim()) {
        yield* this.streamTextWithTypingEffect(textBeforeImage);
      }

      // Check if there's an [IMAGE_END] marker in this chunk
      if (imageDataStart.includes('[IMAGE_END]')) {
        const endIndex = imageDataStart.indexOf('[IMAGE_END]');
        const imageData = imageDataStart.substring(0, endIndex);
        const remainingText = imageDataStart.substring(endIndex + '[IMAGE_END]'.length);

        // Complete image with end marker (no delay)
        if (imageData.trim()) {
          yield {
            type: 'image',
            content: imageData.trim(),
            metadata: { imageStart: true, imageEnd: true },
          };
        }

        // Stream remaining text with typing effect
        if (remainingText.trim()) {
          yield* this.streamTextWithTypingEffect(remainingText);
        }
      } else {
        // Start image processing without end marker (no delay for image data)
        yield {
          type: 'image',
          content: imageDataStart,
          metadata: { imageStart: true },
        };
      }
      return;
    }

    // Handle explicit image end (no throttling for image data, for backward compatibility)
    if (chunk.includes('[IMAGE_END]')) {
      const endIndex = chunk.indexOf('[IMAGE_END]');
      const imageData = chunk.substring(0, endIndex);
      const remainingText = chunk.substring(endIndex + '[IMAGE_END]'.length);

      // Complete image (no delay)
      if (imageData.trim()) {
        yield {
          type: 'image',
          content: imageData.trim(),
          metadata: { imageEnd: true },
        };
      }

      // Stream remaining text with typing effect
      if (remainingText.trim()) {
        yield* this.streamTextWithTypingEffect(remainingText);
      }
      return;
    }

    // Handle regular text chunks with typing effect
    if (chunk.trim()) {
      yield* this.streamTextWithTypingEffect(chunk);
    }
  }

  // Stream text with character-by-character typing effect
  private async* streamTextWithTypingEffect(text: string): AsyncGenerator<StreamingChunk, void, unknown> {
    const words = text.split(/(\s+)/); // Split by whitespace but keep the whitespace

    for (const word of words) {
      yield {
        type: 'text',
        content: word,
      };

      // Add delay between words for natural typing rhythm
      if (word.trim()) { // Only delay for actual words, not whitespace
        await this.delay(this.throttleConfig.characterDelay * word.length);
      }
    }
  }

  // Utility method for delays
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Update configuration
  updateConfig(config: Partial<StreamingConfig>): void {
    this.config = { ...this.config, ...config };
  }

  // Update throttle configuration
  updateThrottleConfig(config: Partial<StreamingThrottleConfig>): void {
    this.throttleConfig = { ...this.throttleConfig, ...config };
  }

  // Get current configuration
  getConfig(): StreamingConfig {
    return { ...this.config };
  }
}

// Export singleton instance
export const streamingService = new StreamingServiceImpl();

// Export class for custom instances
export { StreamingServiceImpl };

// Service factory
export const createStreamingService = (config?: Partial<StreamingConfig>): StreamingService => {
  return new StreamingServiceImpl(config);
};

// Default export
export default streamingService;
