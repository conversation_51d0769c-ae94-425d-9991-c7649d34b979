// Homepage Component Styles - Eneco Brand

.homepage {
  min-height: 100vh;
  background: var(--color-background);
  font-family: var(--font-family-sans);
}

// Header
.homepage__header {
  background: var(--color-background-primary);
  color: var(--color-text-primary);
  padding: var(--spacing-4) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  @include shadow(md);
  border-bottom: 1px solid var(--color-border);
}

.homepage__header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.homepage__logo {
  display: flex;
  align-items: center;
}

.homepage__logo-image {
  height: 60px;
  width: auto;
  object-fit: contain;
}

.homepage__nav {
  display: flex;
  gap: var(--spacing-8);
  
  @include mobile {
    display: none;
  }
}

.homepage__nav-link {
  color: var(--color-text-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  @include transition();

  &:hover {
    color: var(--color-text-brand);
  }
}

.homepage__login-btn {
  background: var(--color-primary);
  color: var(--color-text-inverted);
  border: 1px solid var(--color-primary);
  @include padding-x(4);
  @include padding-y(2);
  @include rounded(md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  @include transition();

  &:hover {
    background: var(--color-primary-hover);
    border-color: var(--color-primary-hover);
  }
}

// Hero Section
.homepage__hero {
  background: linear-gradient(135deg, var(--color-eneco-red) 0%, var(--color-secondary) 100%);
  color: var(--color-text-on-primary);
  padding: var(--spacing-20) 0;
  position: relative;
  overflow: hidden;
}

.homepage__hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
}

.homepage__hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-16);
  align-items: center;
  
  @include mobile {
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
  }
}

.homepage__hero-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-6);
  max-width: 450px;
  
  @include mobile {
    font-size: var(--font-size-3xl);
  }
}

.homepage__hero-features {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--spacing-8) 0;
  
  li {
    margin-bottom: var(--spacing-2);
    font-weight: var(--font-weight-medium);
  }
}

.homepage__hero-form {
  background: white;
  @include padding(6);
  @include rounded(lg);
  @include shadow(xl);
  color: var(--color-text-primary);
}

.homepage__form-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  
  @include mobile {
    grid-template-columns: 1fr;
  }
}

.homepage__form-select {
  @include padding(3);
  border: 1px solid var(--color-border);
  @include rounded(md);
  background: white;
  font-size: var(--font-size-base);
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

.homepage__form-actions {
  display: flex;
  gap: var(--spacing-4);
  
  @include mobile {
    flex-direction: column;
  }
}

.homepage__btn {
  @include padding-x(6);
  @include padding-y(3);
  @include rounded(md);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  @include transition();
  border: none;
  font-size: var(--font-size-base);
  
  &--primary {
    background: var(--color-eneco-green);
    color: white;

    &:hover {
      background: var(--color-accent-green-800);
      transform: translateY(-1px);
    }
  }
  
  &--secondary {
    background: transparent;
    color: var(--color-eneco-red);
    border: 2px solid var(--color-eneco-red);

    &:hover {
      background: var(--color-eneco-red);
      color: var(--color-text-on-primary);
    }
  }
}

.homepage__hero-note {
  margin-top: var(--spacing-4);
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.homepage__hero-image {
  display: flex;
  justify-content: center;
  align-items: center;

  // Ensure the flip animation doesn't affect layout
  min-height: 400px; // Maintain consistent height

  @include mobile {
    min-height: 300px;
  }
}

// Edwin flip animation container
.homepage__edwin-container {
  perspective: 1000px;
  cursor: pointer;
  @include transition(transform, 0.2s, ease);
  position: relative;

  // Add a subtle glow effect to indicate interactivity
  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(45deg, var(--color-eneco-green), var(--color-primary));
    border-radius: var(--border-radius-lg);
    opacity: 0;
    z-index: -1;
    @include transition(opacity, 0.3s, ease);
  }

  &:hover {
    transform: scale(1.02);

    &::before {
      opacity: 0.1;
    }
  }

  &:focus {
    outline: 3px solid var(--color-primary);
    outline-offset: 4px;
    border-radius: var(--border-radius-lg);

    &::before {
      opacity: 0.15;
    }
  }

  &:active {
    transform: scale(0.98);
  }

  &--animating {
    pointer-events: none; // Prevent clicks during animation

    &::before {
      opacity: 0.2;
    }
  }

  // Add a subtle hint text for first-time users
  &::after {
    content: 'Click to reveal';
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: var(--font-size-sm);
    color: var(--color-text-on-primary);
    opacity: 0.7;
    @include transition(opacity, 0.3s, ease);
    pointer-events: none;
    white-space: nowrap;

    @include mobile {
      content: 'Tap to reveal';
      bottom: -25px;
      font-size: var(--font-size-xs);
    }
  }

  &:hover::after {
    opacity: 1;
  }

  &--flipped::after {
    content: 'Click to flip back';

    @include mobile {
      content: 'Tap to flip back';
    }
  }

  // Reduce motion for users who prefer it
  @media (prefers-reduced-motion: reduce) {
    &::before,
    &::after {
      @include transition(opacity, 0.1s, ease);
    }

    &:hover {
      transform: none;
    }

    &:active {
      transform: none;
    }
  }
}

// 3D flip card structure
.homepage__edwin-card {
  position: relative;
  width: 100%;
  max-width: 400px;
  height: auto;
  transform-style: preserve-3d;
  @include transition(transform, 0.6s, ease-in-out);

  // Hardware acceleration for smooth animation
  will-change: transform;

  @include mobile {
    max-width: 300px;
  }

  .homepage__edwin-container--flipped & {
    transform: rotateY(180deg);
  }

  // Ensure the card maintains its dimensions during animation
  &::before {
    content: '';
    display: block;
    width: 100%;
    padding-bottom: 100%; // Maintain aspect ratio
    position: absolute;
    top: 0;
    left: 0;
    z-index: -2;
  }

  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
    @include transition(opacity, 0.3s, ease);

    .homepage__edwin-container--flipped & {
      transform: none;
      opacity: 0;
    }

    .homepage__edwin-container--flipped &.homepage__edwin-card-back {
      opacity: 1;
    }
  }
}

// Front and back faces of the card
.homepage__edwin-card-front,
.homepage__edwin-card-back {
  position: relative;
  width: 100%;
  backface-visibility: hidden;
  @include rounded(lg);
  overflow: hidden;

  // Add subtle shadow for depth
  @include shadow(md);
  @include transition(box-shadow, 0.3s, ease);

  // Hardware acceleration
  will-change: transform;

  .homepage__edwin-container:hover & {
    @include shadow(lg);
  }

  // Add a subtle border for definition
  border: 2px solid rgba(255, 255, 255, 0.1);

  // Ensure consistent sizing
  box-sizing: border-box;
}

.homepage__edwin-card-back {
  position: absolute;
  top: 0;
  left: 0;
  transform: rotateY(180deg);

  // Add a special effect for the back card
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
      rgba(0, 114, 80, 0.1) 0%,
      rgba(229, 56, 76, 0.1) 100%);
    z-index: 1;
    pointer-events: none;
  }
}

.homepage__edwin-image {
  width: 100%;
  height: auto;
  object-fit: contain;
  display: block;
  @include rounded(lg);

  // Ensure images maintain aspect ratio during flip
  aspect-ratio: auto;

  // Smooth image rendering
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;

  // Prevent image selection during animation
  user-select: none;
  -webkit-user-drag: none;

  // Ensure consistent positioning
  position: relative;
  z-index: 2; // Above the overlay effect

  // Add subtle transition for any image property changes
  @include transition(opacity, 0.3s, ease);
}

.homepage__person-placeholder {
  width: 300px;
  height: 400px;
  background: linear-gradient(45deg, var(--color-white-opacity-15), var(--color-white-opacity-30));
  @include rounded(lg);
  border: 2px solid var(--color-white-opacity-30);
}

// Services Section
.homepage__services {
  background: white;
  padding: var(--spacing-12) 0;
  border-bottom: 1px solid var(--color-border);
}

.homepage__services-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: var(--spacing-8);
  
  @include mobile {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-4);
  }
}

.homepage__service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: var(--spacing-3);
  @include padding(4);
  @include rounded(lg);
  @include transition();
  cursor: pointer;
  
  &:hover {
    background: var(--color-surface-secondary);
    transform: translateY(-2px);
  }
}

.homepage__service-icon {
  width: 32px;
  height: 32px;
  color: var(--color-eneco-red);
}

.homepage__service-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

// Benefits Section
.homepage__benefits {
  padding: var(--eneco-section-padding) 0;
  background: var(--color-surface-secondary);
}

.homepage__benefits-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
}

.homepage__benefits-title {
  text-align: center;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-16);
}

.homepage__benefits-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  gap: var(--spacing-8);
  max-width: 1000px;
  margin: 0 auto;

  @include mobile {
    flex-direction: column;
    align-items: center;
  }
}

.homepage__benefit-card {
  background: white;
  padding: var(--spacing-4); /* 16px as requested */
  @include rounded(lg);
  @include shadow(md);
  @include transition();
  flex: 0 1 450px;
  width: 450px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: var(--spacing-4);
  align-items: center;

  @include mobile {
    width: 100%;
    max-width: 450px;
    flex: none;
  }

  &:hover {
    @include shadow(lg);
    transform: translateY(-2px);
  }

  h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    margin: 0 0 var(--spacing-3) 0;
  }

  p {
    color: var(--color-text-secondary);
    line-height: var(--line-height-relaxed);
    margin: 0;
  }
}

.homepage__benefit-icon {
  width: 48px;
  height: 48px;
  min-width: 48px; /* Prevent shrinking */
  min-height: 48px; /* Prevent shrinking */
  background: var(--color-eneco-red);
  color: var(--color-text-on-primary);
  @include rounded(lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* Prevent flexbox from shrinking the icon */

  svg {
    width: 24px;
    height: 24px;
  }
}

.homepage__benefit-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}
