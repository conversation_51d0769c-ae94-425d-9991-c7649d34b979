@import '../tokens/colors';
@import '../tokens/spacing';
@import '../tokens/typography';

.image-skeleton {
  margin: $spacing-2 0;
  
  &__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: $spacing-6;
    background-color: $neutral-50;
    border: 1px solid $neutral-200;
    border-radius: 8px;
    min-height: 120px;
    justify-content: center;
  }
  
  &__placeholder {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: $neutral-100;
    border-radius: 8px;
    color: $neutral-400;
    margin-bottom: $spacing-2;
  }
  
  &__icon {
    position: absolute;
    z-index: 1;
  }
  
  &__loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
  }
  
  &__spinner {
    width: 24px;
    height: 24px;
    border: 2px solid $neutral-200;
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: image-skeleton-spin 1s linear infinite;
  }
  
  &__message {
    font-size: $font-size-sm;
    color: $neutral-600;
    text-align: center;
    font-weight: $font-weight-medium;
  }
}

@keyframes image-skeleton-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .image-skeleton {
    &__container {
      background-color: $neutral-800;
      border-color: $neutral-700;
    }
    
    &__placeholder {
      background-color: $neutral-700;
      color: $neutral-500;
    }
    
    &__loading {
      background-color: rgba(0, 0, 0, 0.8);
    }
    
    &__spinner {
      border-color: $neutral-600;
      border-top-color: var(--color-primary);
    }
    
    &__message {
      color: $neutral-300;
    }
  }
}
