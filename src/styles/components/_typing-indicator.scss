// Typing Indicator Component Styles

.typing-indicator {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-2);
  margin-bottom: var(--chat-message-spacing);
  @include fade-in;
}

.typing-indicator__content {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-2);
}

.typing-indicator__avatar {
  width: 32px;
  height: 32px;
  @include rounded(full);
  background: var(--color-background-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 1px solid var(--color-border);
}

.typing-indicator__avatar-icon {
  font-size: 16px;
  line-height: 1;
}

.typing-indicator__avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.typing-indicator__bubble {
  background: var(--color-chat-agent-bg);
  color: var(--color-chat-agent-text);
  @include padding(3);
  @include rounded-r(lg);
  @include rounded-t(lg);
  @include rounded-bl(sm);
  @include shadow(sm);
  position: relative;
  min-width: 60px;
}

.typing-indicator__dots {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  margin-bottom: var(--spacing-1);
}

.typing-indicator__dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.4;
  animation: typing-dot 1.4s infinite ease-in-out;
  
  &:nth-child(1) {
    animation-delay: 0s;
  }
  
  &:nth-child(2) {
    animation-delay: 0.2s;
  }
  
  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

.typing-indicator__text {
  @include text-xs;
  color: var(--chat-agent-text);
  font-style: italic;
}

@keyframes typing-dot {
  0%, 60%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// Alternative wave animation
.typing-indicator--wave {
  .typing-indicator__dot {
    animation: typing-wave 1.4s infinite ease-in-out;
  }
}

@keyframes typing-wave {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

// Pulse animation variant
.typing-indicator--pulse {
  .typing-indicator__bubble {
    animation: typing-pulse 2s infinite ease-in-out;
  }
  
  .typing-indicator__dot {
    animation: none;
    opacity: 0.6;
  }
}

@keyframes typing-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// Responsive adjustments
@include mobile {
  .typing-indicator__avatar {
    width: 28px;
    height: 28px;
  }
  
  .typing-indicator__avatar-icon {
    font-size: 14px;
  }
  
  .typing-indicator__bubble {
    @include padding(2);
  }
  
  .typing-indicator__dot {
    width: 6px;
    height: 6px;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .typing-indicator__avatar {
    border-width: 2px;
  }
  
  .typing-indicator__bubble {
    border: 1px solid var(--color-border);
  }
  
  .typing-indicator__dot {
    opacity: 0.8;
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .typing-indicator {
    animation: none;
  }
  
  .typing-indicator__dot {
    animation: none;
    opacity: 0.6;
  }
  
  .typing-indicator--pulse .typing-indicator__bubble {
    animation: none;
  }
}

// Dark theme adjustments
[data-theme="dark"] {
  .typing-indicator__avatar {
    background: var(--color-background-secondary);
  }
}
