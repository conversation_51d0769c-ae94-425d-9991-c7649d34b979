/**
 * Test file to verify the streaming image parsing fix
 * This test simulates the problematic response format that was causing UI hangs
 */

import { streamingService } from '../services/streamingService';

// Example response that was causing the issue
const EXAMPLE_RESPONSE = `[STARTED THREAD]: thread_6mEPm8hEBp8OmJGlj7QD45v3[TOOLCALL] visualisation-agent [ENDTOOLCALL][STARTED RESPONSE]: I've created a graph showing the energy usage for your account (customer ID: 64064, account ID: 1) under Eneco over the past half year. Here's a summary of the graph:
- **Electricity Usage (kWh)** shows a significant increase starting from May, peaking in June.
- **Gas Usage (m³)** also demonstrates a sharp increase from April onwards, reaching its highest in June.
If you'd like further clarification or have additional inquiries, please let me know![IMAGE]: iVBORw0KGgoAAAANSUhEUgAAB7wAAAScCAYAAAAYkfANAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/P9b71AAAACXBIWXMAAB7CAAAewgFu0HU+AAEAAElEQVR4nOzdd3xT9f4/8FeS7r1pSyelk7ZQEAFRkCXIEGSIol5x4bpur+t+1eu4jp/zulBwojgABZmKgynIaGlLacvooKV7pCNt0kb8/qgNPUmbJl3paV/Px4PHo+fkfD7nc5rknZL3+bw/Ep1OpwMREREREREREREREREREZHISK09ACIiIiIiIiIiIiIiIiIiou5gwpuIiIiIiIiIiIiIiIiIiESJCW8iIiIiIiIiIiIiIiIiIhIlJryJiIiIiIiIiIiIiIiIiEiUmPAmIiIiIiIiIiIiIiIiIiJRYsKbiIiIiIiIiIiIiIiIiIhEiQlvIiIiIiIiIiIiIiIiIiISJSa8iYiIiIiIiIiIiIiIiIhIlJjwJiIiIiIiIiIiIiIiIiIiUWLCm4iIiIiIiIiIiIiIiIiIRIkJbyIiIiIiIiIiIiIiIiIiEiUmvImIiIiIiIiIiIiIiIiISJSY8CYiIiIiIiIiIiIiIiIiIlFiwpuIiIiIiIiIiIiIiIiIiESJCW8iIiIiIiIiIiIiIiIiIhIlJryJiIiIiIiIiIiIiIiIiEiUmPAmIiIiIiIiIiIiIiIiIiJRYsKbiIiIiIiIiIiIiIiIiIhEiQlvIiIiIiIiIiIiIiIiIiISJRtrD4CIiIiIiIjEr7S0FGfPnkVJSQkUCgU0Gg2cnJzg6+uLsLAwjBgxAhKJxNrDpD700UcfYc2aNfrtO+64A6tWrbLiiIjIlOLiYixYsEC/HRAQgK1bt1rUR3Z2NrZu3Yr09HSUlpVVYU9e/Z0WKq3rq5OMFsQGLzlzIHW3+fTTz+NZ599VvDFb2ZmJjIzM/POO+/A1dUV8fHxGD16NMaNG4eEhASLErItLS149...`;

// Mock fetch to return the example response
const createMockResponse = (text: string): Response => {
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    start(controller) {
      // Simulate chunked streaming by splitting the response
      const chunks = text.match(/.{1,100}/g) || [text]; // Split into 100-char chunks
      
      chunks.forEach((chunk, index) => {
        setTimeout(() => {
          controller.enqueue(encoder.encode(chunk));
          if (index === chunks.length - 1) {
            controller.close();
          }
        }, index * 10); // 10ms delay between chunks
      });
    }
  });

  return new Response(stream, {
    status: 200,
    headers: { 'Content-Type': 'text/plain' }
  });
};

// Test function to verify the fix
export const testStreamingImageFix = async (): Promise<boolean> => {
  console.log('🧪 Testing streaming image fix...');
  
  try {
    const mockResponse = createMockResponse(EXAMPLE_RESPONSE);
    const chunks: any[] = [];
    let imageChunks = 0;
    let textChunks = 0;
    let hasImageStart = false;
    let hasImageEnd = false;
    
    // Process the streaming response
    for await (const chunk of streamingService.processStreamingResponse(mockResponse)) {
      chunks.push(chunk);
      
      if (chunk.type === 'image') {
        imageChunks++;
        if (chunk.metadata?.imageStart) hasImageStart = true;
        if (chunk.metadata?.imageEnd || chunk.metadata?.streamEnd) hasImageEnd = true;
      } else if (chunk.type === 'text') {
        textChunks++;
        // Check if any text chunk contains base64-like data (which would be bad)
        if (chunk.content.match(/^[A-Za-z0-9+/]{50,}/)) {
          console.error('❌ Found base64 data in text chunk:', chunk.content.substring(0, 100) + '...');
          return false;
        }
      }
    }
    
    console.log(`✅ Processed ${chunks.length} chunks total`);
    console.log(`   - ${textChunks} text chunks`);
    console.log(`   - ${imageChunks} image chunks`);
    console.log(`   - Image start detected: ${hasImageStart}`);
    console.log(`   - Image end detected: ${hasImageEnd}`);
    
    // Verify that we got the expected chunks
    if (!hasImageStart) {
      console.error('❌ No image start detected');
      return false;
    }
    
    if (!hasImageEnd) {
      console.error('❌ No image end detected');
      return false;
    }
    
    if (imageChunks === 0) {
      console.error('❌ No image chunks detected');
      return false;
    }
    
    if (textChunks === 0) {
      console.error('❌ No text chunks detected');
      return false;
    }
    
    console.log('✅ Streaming image fix test passed!');
    return true;
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
};

// Export for use in other tests or manual testing
export { EXAMPLE_RESPONSE, createMockResponse };
