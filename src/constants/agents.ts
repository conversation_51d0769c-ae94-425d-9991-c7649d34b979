// Agent constants and descriptions for Edwin chatbot

export interface Agent {
  id: string;
  name: string;
  description: string;
  typingMessage: string;
}

export const AGENTS: Record<string, Agent> = {
  'orchestrator-agent': {
    id: 'orchestrator-agent',
    name: '🪄 Orchestrator Agent',
    description: 'Analyzes the user question and calls the right set of agents to answer the question properly.',
    typingMessage: '<PERSON> is processing your request...'
  },
  'visualisation-agent': {
    id: 'visualisation-agent',
    name: '📊 Visualization Agent', 
    description: 'Generates graphs or charts to support the conversation and create clarity on installment amounts.',
    typingMessage: '<PERSON> is generating a visualization...'
  },
  'scenario-agent': {
    id: 'scenario-agent',
    name: '📅 Scenario Agent',
    description: 'Uses the MCP server to provide the user with next steps or recommendations based on the user\'s data.',
    typingMessage: '<PERSON> is analyzing your scenario...'
  },
  'installment-amount-agent': {
    id: 'installment-amount-agent',
    name: '💵 Installment Amount Agent',
    description: 'Calculates and provides installment amount recommendations.',
    typingMessage: '<PERSON> is calculating installment amounts...'
  },
  'installment-amount-rule-evaluation-agent': {
    id: 'installment-amount-rule-evaluation-agent',
    name: '📕 Rule Evaluation Agent',
    description: 'Evaluates the rules and policies that apply to the user\'s situation and provides guidance accordingly.',
    typingMessage: '<PERSON> is evaluating applicable rules...'
  }
};

// Helper function to get agent typing message
export const getAgentTypingMessage = (agentId: string): string => {
  const agent = AGENTS[agentId];
  return agent ? agent.typingMessage : 'Edwin is typing...';
};

// Helper function to get agent name
export const getAgentName = (agentId: string): string => {
  const agent = AGENTS[agentId];
  return agent ? agent.name : 'Edwin';
};

// Helper function to get agent description
export const getAgentDescription = (agentId: string): string => {
  const agent = AGENTS[agentId];
  return agent ? agent.description : 'AI Assistant';
};

// Default typing message when no specific agent is active
export const DEFAULT_TYPING_MESSAGE = 'Edwin is typing...';
