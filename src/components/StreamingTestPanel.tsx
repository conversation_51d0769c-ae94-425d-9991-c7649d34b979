import React, { useState } from 'react';
import { useChatStore } from '../store/chatStore';
import { mockStreamingService } from '../services/mockStreamingService';
import { useStreaming } from '../hooks/useStreaming';

interface StreamingTestPanelProps {
  className?: string;
}

const StreamingTestPanel: React.FC<StreamingTestPanelProps> = ({ className }) => {
  const { streamingEnabled, setStreamingEnabled, addMessage, setTyping } = useChatStore();
  const { processStreamingResponse } = useStreaming();
  const [isTestRunning, setIsTestRunning] = useState(false);
  const [runningTestId, setRunningTestId] = useState<string | null>(null);

  const testScenarios = [
    {
      id: 'basic',
      name: 'Basic Streaming',
      description: 'Test basic streaming with tool calls',
      action: async () => {
        const response = await mockStreamingService.createMockStreamingResponse({
          message: 'Test basic streaming',
          accountId: '2',
          customerId: '********',
        });
        return response;
      }
    },
    {
      id: 'with-images',
      name: 'Streaming with Images',
      description: 'Test streaming response that includes images',
      action: async () => {
        const response = await mockStreamingService.createMockStreamingResponse({
          message: 'Show me a chart',
          accountId: '2',
          customerId: '********',
        });
        return response;
      }
    },
    {
      id: 'image-fix-test',
      name: 'Image Fix Test',
      description: 'Test the fix for streaming responses with base64 images without [IMAGE_END] markers',
      action: async () => {
        // Use the problematic response that was causing UI hangs
        return createMockResponse(EXAMPLE_RESPONSE);
      }
    },
    {
      id: 'error-timeout',
      name: 'Timeout Error',
      description: 'Test timeout error handling',
      action: async () => {
        return mockStreamingService.createMockErrorResponse('timeout');
      }
    },
    {
      id: 'error-network',
      name: 'Network Error',
      description: 'Test network error handling',
      action: async () => {
        return mockStreamingService.createMockErrorResponse('network');
      }
    },
    {
      id: 'error-server',
      name: 'Server Error',
      description: 'Test server error handling',
      action: async () => {
        return mockStreamingService.createMockErrorResponse('server');
      }
    },
    {
      id: 'error-interrupted',
      name: 'Interrupted Stream',
      description: 'Test interrupted stream handling',
      action: async () => {
        return mockStreamingService.createMockErrorResponse('interrupted');
      }
    }
  ];

  const runTest = async (scenario: typeof testScenarios[0]) => {
    if (isTestRunning) return;

    setIsTestRunning(true);
    setRunningTestId(scenario.id);

    try {
      // Add user message
      addMessage(`Testing: ${scenario.name}`, 'user');

      // Show typing indicator
      setTyping(true);

      // Run the test scenario
      const response = await scenario.action();

      // Check if response is valid
      if (!response) {
        throw new Error('No response received from test scenario');
      }

      // Process the streaming response
      await processStreamingResponse(response);

    } catch (error) {
      console.error('Test scenario failed:', error);
      setTyping(false);

      // Add error message with more details
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      addMessage(`❌ Test "${scenario.name}" failed: ${errorMessage}`, 'agent');
    } finally {
      setIsTestRunning(false);
      setRunningTestId(null);
    }
  };

  if (!import.meta.env.DEV) {
    return null; // Only show in development
  }

  return (
    <div className={`streaming-test-panel ${className || ''}`}>
      <div className="streaming-test-panel__header">
        <h3>🧪 Streaming Test Panel</h3>
        <div className="streaming-test-panel__status">
          <label>
            <input
              type="checkbox"
              checked={streamingEnabled}
              onChange={(e) => setStreamingEnabled(e.target.checked)}
            />
            Streaming Enabled
          </label>
        </div>
      </div>
      
      <div className="streaming-test-panel__scenarios">
        {testScenarios.map((scenario) => (
          <div key={scenario.id} className="streaming-test-panel__scenario">
            <div className="streaming-test-panel__scenario-info">
              <h4>{scenario.name}</h4>
              <p>{scenario.description}</p>
            </div>
            <button
              onClick={() => runTest(scenario)}
              disabled={isTestRunning || !streamingEnabled}
              className="streaming-test-panel__test-button"
            >
              {runningTestId === scenario.id ? 'Running...' : 'Test'}
            </button>
          </div>
        ))}
      </div>
      
      <div className="streaming-test-panel__info">
        <p><strong>Note:</strong> Make sure streaming is enabled to test these scenarios.</p>
        <p>Mock streaming is automatically enabled in development mode.</p>
      </div>
    </div>
  );
};

export default StreamingTestPanel;
