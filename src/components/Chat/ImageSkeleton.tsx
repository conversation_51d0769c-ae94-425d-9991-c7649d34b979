import React from 'react';
import clsx from 'clsx';

interface ImageSkeletonProps {
  className?: string;
  message?: string;
}

const ImageSkeleton: React.FC<ImageSkeletonProps> = ({ 
  className,
  message = "Processing image..." 
}) => {
  return (
    <div className={clsx('image-skeleton', className)}>
      <div className="image-skeleton__container">
        <div className="image-skeleton__placeholder">
          <div className="image-skeleton__icon">
            <svg 
              width="48" 
              height="48" 
              viewBox="0 0 24 24" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2" fill="none"/>
              <circle cx="8.5" cy="8.5" r="1.5" fill="currentColor"/>
              <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" stroke="currentColor" strokeWidth="2" fill="none"/>
            </svg>
          </div>
          <div className="image-skeleton__loading">
            <div className="image-skeleton__spinner"></div>
          </div>
        </div>
        <div className="image-skeleton__message">
          {message}
        </div>
      </div>
    </div>
  );
};

export default ImageSkeleton;
