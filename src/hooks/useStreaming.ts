import { useCallback, useRef } from 'react';
import { useChatStore } from '../store/chatStore';
import { streamingService } from '../services/streamingService';
import type { StreamingChunk, StreamingState } from '../types/api';

interface UseStreamingOptions {
  onStreamStart?: () => void;
  onStreamEnd?: () => void;
  onStreamError?: (error: Error) => void;
  onChunk?: (chunk: StreamingChunk) => void;
}

export const useStreaming = (options: UseStreamingOptions = {}) => {
  const {
    addMessage,
    setTyping,
    setThreadId,
    setCurrentAgent,
  } = useChatStore();

  const streamingStateRef = useRef<StreamingState>({
    streamedText: '',
    isReceivingImage: false,
    imageBuffer: '',
    pendingImages: [],
    hasReceivedFirstContent: false,
    currentTool: '',
    responseStarted: false,
    hasReceivedFirstToolCall: false,
  });

  const currentStreamingMessageIdRef = useRef<string | null>(null);

  const resetStreamingState = useCallback(() => {
    streamingStateRef.current = {
      streamedText: '',
      isReceivingImage: false,
      imageBuffer: '',
      pendingImages: [],
      hasReceivedFirstContent: false,
      currentTool: '',
      responseStarted: false,
      hasReceivedFirstToolCall: false,
    };
    currentStreamingMessageIdRef.current = null;
  }, []);

  const updateStreamingMessage = useCallback((messageId: string, content: string, images?: string[], toolInfo?: string, isProcessingImage?: boolean) => {
    const { messages: currentMessages } = useChatStore.getState();
    const messageIndex = currentMessages.findIndex(msg => msg.id === messageId);

    if (messageIndex !== -1) {
      const updatedMessages = [...currentMessages];
      updatedMessages[messageIndex] = {
        ...updatedMessages[messageIndex],
        content,
        ...(images && { images }),
        ...(toolInfo && { toolInfo }),
        isStreaming: true,
        isComplete: false,
        isProcessingImage: isProcessingImage ?? false,
      };

      // Update the store directly
      useChatStore.setState({ messages: updatedMessages });
    }
  }, []);

  const completeStreamingMessage = useCallback((messageId: string) => {
    const { messages: currentMessages } = useChatStore.getState();
    const messageIndex = currentMessages.findIndex(msg => msg.id === messageId);
    
    if (messageIndex !== -1) {
      const updatedMessages = [...currentMessages];
      updatedMessages[messageIndex] = {
        ...updatedMessages[messageIndex],
        isStreaming: false,
        isComplete: true,
      };
      
      useChatStore.setState({ messages: updatedMessages });
    }
  }, []);

  const handleChunk = useCallback((chunk: StreamingChunk) => {
    const state = streamingStateRef.current;
    
    options.onChunk?.(chunk);

    switch (chunk.type) {
      case 'thread_id':
        setThreadId(chunk.content);
        break;

      case 'tool_call':
        state.currentTool = chunk.content;
        state.hasReceivedFirstToolCall = true;

        // Set current agent and update typing indicator
        setCurrentAgent(chunk.content);
        setTyping(true, chunk.content);

        // Update typing indicator with tool info
        if (currentStreamingMessageIdRef.current) {
          let toolInfo = chunk.content;
          
          // Provide user-friendly tool descriptions
          if (chunk.content === 'visualisation-agent') {
            toolInfo = 'Using the Visualisation Agent to generate a nice looking chart, this can take a few seconds...';
          } else if (chunk.content === 'scenario-agent') {
            toolInfo = 'Using the Scenario Agent to analyze the current scenario, please hold on...';
            } else if (chunk.content === 'orchestrator-agent') {
            toolInfo = 'Using the Scenario Agent to analyze the current scenario, please hold on...';
          } else if (chunk.content === 'installment-amount-agent') {
            toolInfo = 'Using the Installment Amount Agent to evaluate the possibilities, please give me a few seconds...';
          }
          
          updateStreamingMessage(currentStreamingMessageIdRef.current, '', undefined, toolInfo);
        }
        break;

      case 'response_start':
        state.responseStarted = true;

        // Remove typing indicator and start streaming message
        if (!state.hasReceivedFirstContent) {
          state.hasReceivedFirstContent = true;
          setTyping(false);
          setCurrentAgent(null); // Clear agent when response starts

          // Add initial streaming message and get its ID
          addMessage('', 'agent');

          // Get the ID of the message we just added
          const { messages } = useChatStore.getState();
          const lastMessage = messages[messages.length - 1];
          currentStreamingMessageIdRef.current = lastMessage.id;

          // Update the message to be streaming
          setTimeout(() => {
            updateStreamingMessage(lastMessage.id, '', [], state.currentTool);
          }, 0);
        }
        break;

      case 'text':
        if (state.responseStarted) {
          // Remove typing indicator on first content
          if (!state.hasReceivedFirstContent) {
            state.hasReceivedFirstContent = true;
            setTyping(false);

            // Add initial streaming message and get its ID
            addMessage('', 'agent');

            // Get the ID of the message we just added
            const { messages } = useChatStore.getState();
            const lastMessage = messages[messages.length - 1];
            currentStreamingMessageIdRef.current = lastMessage.id;

            // Update the message to be streaming
            setTimeout(() => {
              updateStreamingMessage(lastMessage.id, '', [], state.currentTool);
            }, 0);
          }

          state.streamedText += chunk.content;

          if (currentStreamingMessageIdRef.current) {
            updateStreamingMessage(
              currentStreamingMessageIdRef.current,
              state.streamedText,
              state.pendingImages.length > 0 ? [...state.pendingImages] : undefined,
              state.currentTool
            );
          }
        }
        break;

      case 'image':
        if (chunk.metadata?.imageStart) {
          state.isReceivingImage = true;
          state.imageBuffer = chunk.content;

          // Show image processing indicator
          if (currentStreamingMessageIdRef.current) {
            updateStreamingMessage(
              currentStreamingMessageIdRef.current,
              state.streamedText,
              [...state.pendingImages],
              state.currentTool,
              true // isProcessingImage
            );
          }

          // If this is a complete image (both start and end in one chunk)
          if (chunk.metadata?.imageEnd) {
            if (state.imageBuffer.trim()) {
              state.pendingImages.push(state.imageBuffer.trim());
            }
            state.imageBuffer = '';
            state.isReceivingImage = false;

            // Update message with new image and remove processing indicator
            if (currentStreamingMessageIdRef.current) {
              updateStreamingMessage(
                currentStreamingMessageIdRef.current,
                state.streamedText,
                [...state.pendingImages],
                state.currentTool,
                false // isProcessingImage
              );
            }
          }
        } else if (chunk.metadata?.imageEnd || chunk.metadata?.streamEnd) {
          // Handle image end (explicit marker or stream end)
          state.imageBuffer += chunk.content;
          if (state.imageBuffer.trim()) {
            state.pendingImages.push(state.imageBuffer.trim());
          }
          state.imageBuffer = '';
          state.isReceivingImage = false;

          // Update message with new image and remove processing indicator
          if (currentStreamingMessageIdRef.current) {
            updateStreamingMessage(
              currentStreamingMessageIdRef.current,
              state.streamedText,
              [...state.pendingImages],
              state.currentTool,
              false // isProcessingImage
            );
          }
        } else if (state.isReceivingImage || chunk.metadata?.imageContinuation) {
          // Continue accumulating image data (keep processing indicator)
          state.imageBuffer += chunk.content;
          // Don't update the UI for image continuation chunks to prevent rendering base64 as text
        }
        break;
    }
  }, [addMessage, setTyping, setThreadId, updateStreamingMessage, options]);

  const processStreamingResponse = useCallback(async (response: Response) => {
    try {
      options.onStreamStart?.();
      resetStreamingState();

      // Check if response is still valid
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Process the streaming response
      for await (const chunk of streamingService.processStreamingResponse(response)) {
        handleChunk(chunk);
      }

      // Complete the streaming message
      if (currentStreamingMessageIdRef.current) {
        completeStreamingMessage(currentStreamingMessageIdRef.current);
      }

      options.onStreamEnd?.();
    } catch (error) {
      console.error('Streaming error:', error);

      // Handle error - complete any partial message and show error
      if (currentStreamingMessageIdRef.current) {
        completeStreamingMessage(currentStreamingMessageIdRef.current);
      }

      setTyping(false);

      // Provide specific error messages based on error type
      let errorMessage = 'Sorry, I encountered an error while streaming the response. Please try again.';

      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.message.includes('timed out')) {
          errorMessage = 'The response took too long. Please try again with a shorter message.';
        } else if (error.message.includes('Network error') || error.message.includes('fetch')) {
          errorMessage = 'Unable to connect to the server. Please check your internet connection and try again.';
        } else if (error.message.includes('interrupted')) {
          errorMessage = 'The response was interrupted. Please try sending your message again.';
        } else if (error.message.includes('HTTP 429')) {
          errorMessage = 'Too many requests. Please wait a moment before trying again.';
        } else if (error.message.includes('HTTP 5')) {
          errorMessage = 'Server error. Please try again in a few moments.';
        }
      }

      addMessage(errorMessage, 'agent');

      options.onStreamError?.(error as Error);
    } finally {
      resetStreamingState();
    }
  }, [handleChunk, resetStreamingState, completeStreamingMessage, addMessage, setTyping, options]);

  return {
    processStreamingResponse,
    resetStreamingState,
  };
};

export default useStreaming;
